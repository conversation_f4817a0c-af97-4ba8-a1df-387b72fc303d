"""
<<<<<<< HEAD
Mock Objects for Agent Testing

This module provides mock objects and utilities for testing agents,
including mock LLMs, databases, tools, and external services.

Key Features:
- Mock LLM with configurable responses
- Mock database with in-memory storage
- Mock tools with customizable behavior
- Mock external services
- Configurable delays and errors
- Response recording and verification

Usage:
    from backend.agents.shared.testing.mocks import (
        MockLLM,
        MockDatabase,
        MockTool,
        create_mock_config
    )
    
    # Create mock LLM
    mock_llm = MockLLM()
    mock_llm.set_response("Hello! How can I help you?")
    
    # Create mock tool
    mock_tool = MockTool("search", {"results": ["result1", "result2"]})
"""

from typing import Dict, Any, Optional, List, Callable, Union
from unittest.mock import MagicMock, AsyncMock
import time
import asyncio
import json
import uuid
from datetime import datetime, timezone

# Local imports with fallback
try:
    from ..core.base_agent import BaseAgent
    from ..core.state import AgentExecutionContext, AgentStatus
    from ..core.exceptions import AgentExecutionError, AgentTimeoutError
except ImportError:
    try:
        from core.base_agent import BaseAgent
        from core.state import AgentExecutionContext, AgentStatus
        from core.exceptions import AgentExecutionError, AgentTimeoutError
    except ImportError:
        # Minimal fallbacks
        class BaseAgent:
            def __init__(self, **kwargs):
                for k, v in kwargs.items():
                    setattr(self, k, v)

        class AgentExecutionContext: pass
        class AgentStatus:
            COMPLETED = "completed"
            FAILED = "failed"

        class AgentExecutionError(Exception): pass
        class AgentTimeoutError(Exception): pass


class MockLLM:
    """Mock LLM for testing agent interactions."""
    
    def __init__(self):
        self.responses = []
        self.call_history = []
        self.response_delay = 0.0
        self.error_rate = 0.0
        self.current_response_index = 0
    
    def set_response(self, response: str):
        """Set a single response."""
        self.responses = [response]
        self.current_response_index = 0
    
    def set_responses(self, responses: List[str]):
        """Set multiple responses (will cycle through them)."""
        self.responses = responses
        self.current_response_index = 0
    
    def set_response_delay(self, delay: float):
        """Set delay for responses (simulates LLM latency)."""
        self.response_delay = delay
    
    def set_error_rate(self, rate: float):
        """Set error rate (0.0 to 1.0)."""
        self.error_rate = rate
    
    async def generate(self, prompt: str, **kwargs) -> str:
        """Generate a response to a prompt."""
        # Record the call
        call_record = {
            "prompt": prompt,
            "kwargs": kwargs,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
        self.call_history.append(call_record)
        
        # Simulate delay
        if self.response_delay > 0:
            await asyncio.sleep(self.response_delay)
        
        # Simulate errors
        import random
        if random.random() < self.error_rate:
            raise AgentExecutionError("Mock LLM error")
        
        # Return response
        if not self.responses:
            return "Mock LLM response"
        
        response = self.responses[self.current_response_index % len(self.responses)]
        self.current_response_index += 1
        
        return response
    
    def generate_sync(self, prompt: str, **kwargs) -> str:
        """Synchronous version of generate."""
        return asyncio.run(self.generate(prompt, **kwargs))
    
    def get_call_history(self) -> List[Dict[str, Any]]:
        """Get history of all calls."""
        return self.call_history.copy()
    
    def get_call_count(self) -> int:
        """Get number of calls made."""
        return len(self.call_history)
    
    def reset(self):
        """Reset the mock LLM state."""
        self.responses = []
        self.call_history = []
        self.current_response_index = 0
        self.response_delay = 0.0
        self.error_rate = 0.0


class MockDatabase:
    """Mock database for testing agent data operations."""
    
    def __init__(self):
        self.data = {}
        self.query_history = []
        self.query_delay = 0.0
        self.error_rate = 0.0
    
    def set_data(self, table: str, data: List[Dict[str, Any]]):
        """Set data for a table."""
        self.data[table] = data
    
    def set_query_delay(self, delay: float):
        """Set delay for queries."""
        self.query_delay = delay
    
    def set_error_rate(self, rate: float):
        """Set error rate for queries."""
        self.error_rate = rate
    
    async def query(self, sql: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Execute a query."""
        # Record the query
        query_record = {
            "sql": sql,
            "params": params,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
        self.query_history.append(query_record)
        
        # Simulate delay
        if self.query_delay > 0:
            await asyncio.sleep(self.query_delay)
        
        # Simulate errors
        import random
        if random.random() < self.error_rate:
            raise AgentExecutionError("Mock database error")
        
        # Simple query simulation (just return all data for now)
        # In a real mock, you'd parse the SQL and return appropriate data
        table_name = self._extract_table_name(sql)
        return self.data.get(table_name, [])
    
    def query_sync(self, sql: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Synchronous version of query."""
        return asyncio.run(self.query(sql, params))
    
    def _extract_table_name(self, sql: str) -> str:
        """Extract table name from SQL (simple implementation)."""
        sql_lower = sql.lower()
        if "from " in sql_lower:
            parts = sql_lower.split("from ")[1].split()
            return parts[0] if parts else "unknown"
        return "unknown"
    
    def get_query_history(self) -> List[Dict[str, Any]]:
        """Get history of all queries."""
        return self.query_history.copy()
    
    def get_query_count(self) -> int:
        """Get number of queries made."""
        return len(self.query_history)
    
    def reset(self):
        """Reset the mock database state."""
        self.data = {}
        self.query_history = []
        self.query_delay = 0.0
        self.error_rate = 0.0


class MockTool:
    """Mock tool for testing agent tool usage."""
    
    def __init__(self, name: str, return_value: Any = None):
        self.name = name
        self.return_value = return_value
        self.call_history = []
        self.execution_delay = 0.0
        self.error_rate = 0.0
        self.call_count = 0
    
    def set_return_value(self, value: Any):
        """Set the return value for the tool."""
        self.return_value = value
    
    def set_execution_delay(self, delay: float):
        """Set execution delay for the tool."""
        self.execution_delay = delay
    
    def set_error_rate(self, rate: float):
        """Set error rate for the tool."""
        self.error_rate = rate
    
    async def execute(self, *args, **kwargs) -> Any:
        """Execute the tool."""
        self.call_count += 1
        
        # Record the call
        call_record = {
            "tool_name": self.name,
            "args": args,
            "kwargs": kwargs,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "call_number": self.call_count,
        }
        self.call_history.append(call_record)
        
        # Simulate delay
        if self.execution_delay > 0:
            await asyncio.sleep(self.execution_delay)
        
        # Simulate errors
        import random
        if random.random() < self.error_rate:
            raise AgentExecutionError(f"Mock tool '{self.name}' error")
        
        # Return configured value or default
        if self.return_value is not None:
            return self.return_value
        
        return {"tool": self.name, "result": "mock_result", "args": args, "kwargs": kwargs}
    
    def execute_sync(self, *args, **kwargs) -> Any:
        """Synchronous version of execute."""
        return asyncio.run(self.execute(*args, **kwargs))
    
    def get_call_history(self) -> List[Dict[str, Any]]:
        """Get history of all calls."""
        return self.call_history.copy()
    
    def get_call_count(self) -> int:
        """Get number of calls made."""
        return self.call_count
    
    def reset(self):
        """Reset the mock tool state."""
        self.call_history = []
        self.call_count = 0
        self.execution_delay = 0.0
        self.error_rate = 0.0


class MockAgent(BaseAgent):
    """Mock agent for testing agent interactions."""
    
    def __init__(self, agent_type: str = "mock", **kwargs):
        super().__init__(agent_type=agent_type, **kwargs)
        self.initialize_calls = []
        self.execute_calls = []
        self.cleanup_calls = []
        self.execution_delay = 0.0
        self.should_fail = False
        self.failure_message = "Mock agent failure"
    
    def set_execution_delay(self, delay: float):
        """Set execution delay."""
        self.execution_delay = delay
    
    def set_should_fail(self, should_fail: bool, message: str = "Mock agent failure"):
        """Configure the agent to fail."""
        self.should_fail = should_fail
        self.failure_message = message
    
    async def initialize(self, state: Dict[str, Any], config) -> Dict[str, Any]:
        """Mock initialize method."""
        self.initialize_calls.append({
            "state": state.copy(),
            "config": config,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        })
        
        if self.execution_delay > 0:
            await asyncio.sleep(self.execution_delay)
        
        state["initialized"] = True
        return state
    
    async def execute(self, state: Dict[str, Any], config) -> Dict[str, Any]:
        """Mock execute method."""
        self.execute_calls.append({
            "state": state.copy(),
            "config": config,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        })
        
        if self.execution_delay > 0:
            await asyncio.sleep(self.execution_delay)
        
        if self.should_fail:
            raise AgentExecutionError(self.failure_message)
        
        state["executed"] = True
        state["mock_result"] = "Mock execution completed"
        return state
    
    async def cleanup(self, state: Dict[str, Any], config) -> Dict[str, Any]:
        """Mock cleanup method."""
        self.cleanup_calls.append({
            "state": state.copy(),
            "config": config,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        })
        
        if self.execution_delay > 0:
            await asyncio.sleep(self.execution_delay)
        
        state["cleaned_up"] = True
        return state
    
    def get_call_history(self) -> Dict[str, List[Dict[str, Any]]]:
        """Get history of all method calls."""
        return {
            "initialize": self.initialize_calls.copy(),
            "execute": self.execute_calls.copy(),
            "cleanup": self.cleanup_calls.copy(),
        }
    
    def reset(self):
        """Reset the mock agent state."""
        self.initialize_calls = []
        self.execute_calls = []
        self.cleanup_calls = []
        self.execution_delay = 0.0
        self.should_fail = False


def create_mock_config(
    agent_type: str = "mock",
    include_tools: bool = True,
    include_llm: bool = True,
    **kwargs
) -> Dict[str, Any]:
    """Create a mock configuration for testing."""
    config = {
        "agent_type": agent_type,
        "max_execution_time": kwargs.get("max_execution_time", 30),
        "max_iterations": kwargs.get("max_iterations", 10),
        "test_mode": True,
        **kwargs
    }
    
    if include_llm:
        config["llm"] = {
            "provider": "mock",
            "model": "mock-model",
            "temperature": 0.7,
        }
    
    if include_tools:
        config["tools"] = [
            {"name": "mock_search", "description": "Mock search tool"},
            {"name": "mock_calculator", "description": "Mock calculator tool"},
        ]
    
    return config


def create_mock_response_chain(responses: List[str]) -> MockLLM:
    """Create a mock LLM with a chain of responses."""
    mock_llm = MockLLM()
    mock_llm.set_responses(responses)
    return mock_llm


def create_mock_tool_suite(tool_configs: List[Dict[str, Any]]) -> Dict[str, MockTool]:
    """Create a suite of mock tools."""
    tools = {}
    
    for config in tool_configs:
        name = config["name"]
        return_value = config.get("return_value")
        delay = config.get("delay", 0.0)
        error_rate = config.get("error_rate", 0.0)
        
        tool = MockTool(name, return_value)
        tool.set_execution_delay(delay)
        tool.set_error_rate(error_rate)
        
        tools[name] = tool
    
    return tools


def create_slow_mock(delay: float = 2.0) -> MockAgent:
    """Create a mock agent that simulates slow execution."""
    agent = MockAgent()
    agent.set_execution_delay(delay)
    return agent


def create_failing_mock(failure_rate: float = 1.0, message: str = "Simulated failure") -> MockAgent:
    """Create a mock agent that simulates failures."""
    agent = MockAgent()
    agent.set_should_fail(True, message)
    return agent


__all__ = [
    "MockLLM",
    "MockDatabase",
    "MockTool",
    "MockAgent",
    "create_mock_config",
    "create_mock_response_chain",
    "create_mock_tool_suite",
    "create_slow_mock",
    "create_failing_mock",
]
=======
Mock utilities for agent testing.

This module provides comprehensive mock implementations for external
dependencies, services, and components used in agent testing.
"""

import asyncio
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union
from unittest.mock import AsyncMock, MagicMock

from shared.core.base_agent import BaseAgent, AgentConfig
from shared.core.state import AiLexState


class MockManager:
    """
    Centralized mock management for agent testing.
    
    This class provides a unified interface for creating and managing
    mocks for various components used in agent testing.
    """
    
    def __init__(self):
        self.mocks = {}
        self.active_patches = []
    
    def reset_all(self):
        """Reset all mocks to their initial state."""
        for mock in self.mocks.values():
            if hasattr(mock, 'reset_mock'):
                mock.reset_mock()
    
    def cleanup(self):
        """Clean up all mocks and patches."""
        for patch in self.active_patches:
            patch.stop()
        self.active_patches.clear()
        self.mocks.clear()
    
    # ========================================================================
    # Agent Mocks
    # ========================================================================
    
    def create_mock_agent(
        self,
        name: str = "MockAgent",
        config: Optional[AgentConfig] = None
    ) -> MagicMock:
        """
        Create a mock agent with realistic behavior.
        
        Args:
            name: Agent name
            config: Agent configuration
            
        Returns:
            Mock agent instance
        """
        mock_agent = MagicMock(spec=BaseAgent)
        mock_agent.config = config or AgentConfig(name=name)
        mock_agent.name = name
        
        # Mock lifecycle methods
        async def mock_initialize(state, config=None):
            state.add_message("system", f"{name} initialized")
            return state
        
        async def mock_execute(state, config=None):
            state.add_message("assistant", f"{name} executed successfully")
            return state
        
        async def mock_cleanup(state, config=None):
            state.add_message("system", f"{name} cleanup completed")
            return state
        
        mock_agent.initialize = AsyncMock(side_effect=mock_initialize)
        mock_agent.execute = AsyncMock(side_effect=mock_execute)
        mock_agent.cleanup = AsyncMock(side_effect=mock_cleanup)
        
        self.mocks[f"agent_{name}"] = mock_agent
        return mock_agent
    
    # ========================================================================
    # External Service Mocks
    # ========================================================================
    
    def create_mock_supabase_client(self) -> MagicMock:
        """Create a mock Supabase client."""
        mock_client = MagicMock()
        
        # Mock user operations
        mock_client.get_user.return_value = {
            "id": f"user-{uuid.uuid4()}",
            "email": "<EMAIL>",
            "role": "attorney"
        }
        
        # Mock tenant operations
        mock_client.get_tenant.return_value = {
            "id": f"tenant-{uuid.uuid4()}",
            "name": "Test Tenant",
            "settings": {}
        }
        
        # Mock query operations
        mock_client.query.return_value = []
        mock_client.insert.return_value = {"id": f"record-{uuid.uuid4()}"}
        mock_client.update.return_value = {"id": f"record-{uuid.uuid4()}"}
        mock_client.delete.return_value = True
        
        self.mocks["supabase_client"] = mock_client
        return mock_client
    
    def create_mock_pinecone_client(self) -> MagicMock:
        """Create a mock Pinecone client."""
        mock_client = MagicMock()
        mock_index = MagicMock()
        
        # Mock query results
        mock_index.query.return_value = {
            "matches": [
                {
                    "id": f"doc-{i}",
                    "score": 0.9 - (i * 0.1),
                    "metadata": {
                        "text": f"Test document {i} content",
                        "title": f"Document {i}",
                        "type": "legal_document"
                    }
                }
                for i in range(1, 4)
            ]
        }
        
        # Mock upsert operations
        mock_index.upsert.return_value = {"upserted_count": 1}
        
        # Mock delete operations
        mock_index.delete.return_value = {"deleted_count": 1}
        
        mock_client.Index.return_value = mock_index
        self.mocks["pinecone_client"] = mock_client
        return mock_client
    
    def create_mock_openai_client(self) -> MagicMock:
        """Create a mock OpenAI client."""
        mock_client = MagicMock()
        
        # Mock chat completion
        mock_completion = MagicMock()
        mock_completion.choices = [
            MagicMock(
                message=MagicMock(
                    content="This is a test response from the AI assistant.",
                    role="assistant"
                ),
                finish_reason="stop"
            )
        ]
        mock_completion.usage = MagicMock(
            prompt_tokens=50,
            completion_tokens=25,
            total_tokens=75
        )
        
        mock_client.chat.completions.create.return_value = mock_completion
        
        # Mock embeddings
        mock_embedding = MagicMock()
        mock_embedding.data = [
            MagicMock(embedding=[0.1] * 1536)
        ]
        mock_client.embeddings.create.return_value = mock_embedding
        
        self.mocks["openai_client"] = mock_client
        return mock_client
    
    def create_mock_voyage_client(self) -> MagicMock:
        """Create a mock Voyage embeddings client."""
        mock_client = MagicMock()
        
        # Mock embedding methods
        mock_client.embed_query.return_value = [0.1] * 1536
        mock_client.embed_documents.return_value = [[0.1] * 1536] * 3
        
        self.mocks["voyage_client"] = mock_client
        return mock_client
    
    # ========================================================================
    # Tool and Executor Mocks
    # ========================================================================
    
    def create_mock_tool_executor(
        self,
        tool_responses: Optional[Dict[str, Any]] = None
    ) -> AsyncMock:
        """
        Create a mock tool executor.
        
        Args:
            tool_responses: Mapping of tool names to responses
            
        Returns:
            Mock tool executor
        """
        mock_executor = AsyncMock()
        
        async def execute_tool(tool_name: str, tool_args: Dict[str, Any], tenant_id: str):
            if tool_responses and tool_name in tool_responses:
                return tool_responses[tool_name]
            
            # Default response
            return {
                "result": "success",
                "tool_name": tool_name,
                "args": tool_args,
                "tenant_id": tenant_id,
                "execution_time": 0.1
            }
        
        mock_executor.execute_tool.side_effect = execute_tool
        
        self.mocks["tool_executor"] = mock_executor
        return mock_executor
    
    # ========================================================================
    # Database Mocks
    # ========================================================================
    
    def create_mock_database_session(self) -> MagicMock:
        """Create a mock database session."""
        mock_session = MagicMock()
        
        # Mock query operations
        mock_query = MagicMock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.first.return_value = None
        mock_query.all.return_value = []
        mock_query.count.return_value = 0
        
        mock_session.query.return_value = mock_query
        
        # Mock transaction operations
        mock_session.add = MagicMock()
        mock_session.commit = MagicMock()
        mock_session.rollback = MagicMock()
        mock_session.close = MagicMock()
        mock_session.flush = MagicMock()
        
        self.mocks["database_session"] = mock_session
        return mock_session
    
    # ========================================================================
    # State and Configuration Mocks
    # ========================================================================
    
    def create_mock_state_manager(self) -> MagicMock:
        """Create a mock state manager."""
        mock_manager = MagicMock()
        
        # Mock state operations
        mock_manager.save_state.return_value = True
        mock_manager.load_state.return_value = None
        mock_manager.delete_state.return_value = True
        mock_manager.list_states.return_value = []
        
        self.mocks["state_manager"] = mock_manager
        return mock_manager
    
    def create_mock_config_manager(self) -> MagicMock:
        """Create a mock configuration manager."""
        mock_manager = MagicMock()
        
        # Mock configuration operations
        mock_manager.get_config.return_value = {}
        mock_manager.set_config.return_value = True
        mock_manager.delete_config.return_value = True
        mock_manager.list_configs.return_value = []
        
        self.mocks["config_manager"] = mock_manager
        return mock_manager
    
    # ========================================================================
    # Error Simulation
    # ========================================================================
    
    def create_error_mock(
        self,
        error_type: type,
        error_message: str = "Test error",
        call_count: int = 1
    ) -> MagicMock:
        """
        Create a mock that raises an error after specified calls.
        
        Args:
            error_type: Type of error to raise
            error_message: Error message
            call_count: Number of calls before raising error
            
        Returns:
            Mock that raises error
        """
        mock = MagicMock()
        
        def side_effect(*args, **kwargs):
            if mock.call_count >= call_count:
                raise error_type(error_message)
            return {"result": "success"}
        
        mock.side_effect = side_effect
        return mock
    
    # ========================================================================
    # Performance Testing Mocks
    # ========================================================================
    
    def create_slow_mock(self, delay: float = 1.0) -> AsyncMock:
        """
        Create a mock that simulates slow operations.
        
        Args:
            delay: Delay in seconds
            
        Returns:
            Slow async mock
        """
        async def slow_operation(*args, **kwargs):
            await asyncio.sleep(delay)
            return {"result": "success", "delay": delay}
        
        mock = AsyncMock(side_effect=slow_operation)
        self.mocks[f"slow_mock_{delay}"] = mock
        return mock
>>>>>>> origin/setup/testing-infrastructure
