"""
<<<<<<< HEAD
Base Test Classes for Agent Testing

This module provides base test classes and utilities for testing agents,
including setup/teardown, common assertions, and testing patterns.

Key Features:
- Base test class with agent-specific setup
- Common test patterns and assertions
- Mock management and cleanup
- Performance testing utilities
- Error testing patterns
- State validation helpers

Usage:
    from backend.agents.shared.testing.base_test import BaseAgentTest
    
    class TestMyAgent(BaseAgentTest):
        def setUp(self):
            super().setUp()
            self.agent = MyAgent("test")
        
        def test_agent_execution(self):
            result = self.run_agent_test(self.agent, self.sample_input)
            self.assert_agent_success(result)
"""

import unittest
from unittest.mock import MagicMock, patch, AsyncMock
from typing import Dict, Any, Optional, List, Type
import asyncio
import time
from datetime import datetime, timezone

# Local imports with fallback
try:
    from ..core.base_agent import BaseAgent
    from ..core.state import (
        BaseLangGraphState,
        AgentStatus,
        AgentExecutionContext,
        create_execution_context,
    )
    from ..core.exceptions import BaseAgentError, AgentExecutionError
    from .fixtures import (
        create_test_context,
        create_test_state,
        sample_config,
    )
    from .mocks import MockLLM, MockDatabase, MockTool
except ImportError:
    # Fallback for standalone testing
    try:
        from core.base_agent import BaseAgent
        from core.state import (
            BaseLangGraphState,
            AgentStatus,
            AgentExecutionContext,
            create_execution_context,
        )
        from core.exceptions import BaseAgentError, AgentExecutionError
        from fixtures import (
            create_test_context,
            create_test_state,
            sample_config,
        )
        from mocks import MockLLM, MockDatabase, MockTool
    except ImportError:
        # Minimal fallbacks
        class BaseAgent: pass
        class AgentStatus:
            COMPLETED = "completed"
            FAILED = "failed"
        class AgentExecutionContext: pass
        class BaseAgentError(Exception): pass
        class AgentExecutionError(BaseAgentError): pass
        def create_execution_context(**kwargs): return AgentExecutionContext()
        def create_test_context(**kwargs): return AgentExecutionContext()
        def create_test_state(context): return {}
        def sample_config(): return {}
        class MockLLM: pass
        class MockDatabase: pass
        class MockTool: pass


class BaseAgentTest(unittest.TestCase):
    """Base test class for all agent tests."""
    
    def setUp(self):
        """Set up test environment."""
        # Test configuration
        self.test_tenant_id = "test-tenant-123"
        self.test_user_id = "test-user-456"
        self.test_thread_id = "test-thread-789"
        
        # Create test context and state
        self.test_context = create_test_context(
            tenant_id=self.test_tenant_id,
            user_id=self.test_user_id,
            thread_id=self.test_thread_id
        )
        self.test_state = create_test_state(self.test_context)
        self.test_config = sample_config()
        
        # Mock objects
        self.mock_llm = MockLLM()
        self.mock_database = MockDatabase()
        self.mock_tools = {}
        
        # Performance tracking
        self.test_start_time = time.time()
        
        # Patches to clean up
        self.patches = []
    
    def tearDown(self):
        """Clean up test environment."""
        # Stop all patches
        for patch_obj in self.patches:
            patch_obj.stop()
        
        # Clear mock objects
        self.mock_llm.reset()
        self.mock_database.reset()
        
        # Log test duration
        test_duration = time.time() - self.test_start_time
        if test_duration > 5.0:  # Log slow tests
            print(f"SLOW TEST: {self._testMethodName} took {test_duration:.2f}s")
    
    def add_patch(self, target: str, **kwargs) -> MagicMock:
        """Add a patch and track it for cleanup."""
        patch_obj = patch(target, **kwargs)
        mock_obj = patch_obj.start()
        self.patches.append(patch_obj)
        return mock_obj
    
    def create_test_agent(
        self,
        agent_class: Type[BaseAgent],
        agent_type: str = "test",
        config: Optional[Dict[str, Any]] = None
    ) -> BaseAgent:
        """Create a test agent instance."""
        test_config = config or self.test_config
        return agent_class(
            agent_type=agent_type,
            config=test_config
        )
    
    async def run_agent_test(
        self,
        agent: BaseAgent,
        input_data: Optional[Dict[str, Any]] = None,
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Run an agent test with proper setup and error handling."""
        input_data = input_data or {}
        config = config or {
            "configurable": {
                "tenant_id": self.test_tenant_id,
                "user_id": self.test_user_id,
                "thread_id": self.test_thread_id,
            }
        }
        
        try:
            result = await agent.invoke(input_data, config)
            return result
        except Exception as e:
            # Capture and re-raise with context
            self.fail(f"Agent test failed: {e}")
    
    def run_agent_test_sync(
        self,
        agent: BaseAgent,
        input_data: Optional[Dict[str, Any]] = None,
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Run an agent test synchronously."""
        return asyncio.run(self.run_agent_test(agent, input_data, config))
    
    def assert_agent_success(self, result: Dict[str, Any]):
        """Assert that agent execution was successful."""
        self.assertIsInstance(result, dict)
        self.assertIn("status", result)
        self.assertEqual(result["status"], AgentStatus.COMPLETED)
        self.assertNotIn("last_error", result)
    
    def assert_agent_failure(
        self,
        result: Dict[str, Any],
        expected_error_type: Optional[str] = None
    ):
        """Assert that agent execution failed."""
        self.assertIsInstance(result, dict)
        self.assertIn("status", result)
        self.assertEqual(result["status"], AgentStatus.FAILED)
        self.assertIn("last_error", result)
        
        if expected_error_type:
            self.assertEqual(result["last_error"]["error_type"], expected_error_type)
    
    def assert_execution_context_valid(self, context: AgentExecutionContext):
        """Assert that execution context is valid."""
        self.assertIsInstance(context, AgentExecutionContext)
        self.assertIsNotNone(context.tenant_id)
        self.assertIsNotNone(context.user_id)
        self.assertIsNotNone(context.agent_type)
        self.assertIsNotNone(context.execution_id)
        self.assertIsNotNone(context.started_at)
    
    def assert_state_valid(self, state: Dict[str, Any]):
        """Assert that agent state is valid."""
        self.assertIsInstance(state, dict)
        
        # Check required fields
        required_fields = [
            "messages", "execution_context", "status",
            "memory", "context", "errors", "metadata"
        ]
        for field in required_fields:
            self.assertIn(field, state, f"Required field '{field}' missing from state")
        
        # Validate execution context
        if "execution_context" in state:
            self.assert_execution_context_valid(state["execution_context"])
    
    def assert_tenant_isolation(self, result: Dict[str, Any]):
        """Assert that tenant isolation is maintained."""
        execution_context = result.get("execution_context")
        if execution_context:
            self.assertEqual(execution_context.tenant_id, self.test_tenant_id)
    
    def assert_performance_acceptable(
        self,
        execution_time: float,
        max_time: float = 5.0
    ):
        """Assert that execution time is acceptable."""
        self.assertLessEqual(
            execution_time,
            max_time,
            f"Execution time {execution_time:.2f}s exceeds maximum {max_time}s"
        )
    
    def create_mock_tool(self, name: str, return_value: Any = None) -> MockTool:
        """Create a mock tool for testing."""
        mock_tool = MockTool(name, return_value)
        self.mock_tools[name] = mock_tool
        return mock_tool
    
    def simulate_error(
        self,
        error_type: Type[Exception] = AgentExecutionError,
        message: str = "Test error"
    ) -> Exception:
        """Create a test error for error handling tests."""
        return error_type(message)
    
    async def test_agent_lifecycle(self, agent: BaseAgent):
        """Test complete agent lifecycle."""
        # Test initialization
        state = self.test_state.copy()
        config = {"configurable": {"tenant_id": self.test_tenant_id, "user_id": self.test_user_id}}
        
        # Initialize
        state = await agent.initialize(state, config)
        self.assert_state_valid(state)
        
        # Execute
        state = await agent.execute(state, config)
        self.assert_state_valid(state)
        
        # Cleanup
        state = await agent.cleanup(state, config)
        self.assert_state_valid(state)
    
    def test_agent_configuration(self, agent: BaseAgent):
        """Test agent configuration validation."""
        self.assertIsNotNone(agent.agent_type)
        self.assertIsNotNone(agent.agent_name)
        self.assertIsInstance(agent.config, dict)
    
    def test_tenant_isolation(self, agent: BaseAgent):
        """Test tenant isolation enforcement."""
        # This should be implemented by subclasses
        pass
    
    def test_error_handling(self, agent: BaseAgent):
        """Test error handling capabilities."""
        # This should be implemented by subclasses
        pass
    
    def test_performance(self, agent: BaseAgent):
        """Test performance characteristics."""
        # This should be implemented by subclasses
        pass


class AgentTestCase(BaseAgentTest):
    """Extended test case with additional utilities."""
    
    def setUp(self):
        super().setUp()
        
        # Additional test data
        self.test_messages = [
            {"role": "user", "content": "Test message"},
            {"role": "assistant", "content": "Test response"},
        ]
        
        # Performance thresholds
        self.max_execution_time = 5.0
        self.max_memory_usage = 100 * 1024 * 1024  # 100MB
    
    def assert_memory_usage_acceptable(self, memory_usage: int):
        """Assert that memory usage is acceptable."""
        self.assertLessEqual(
            memory_usage,
            self.max_memory_usage,
            f"Memory usage {memory_usage} bytes exceeds maximum {self.max_memory_usage} bytes"
        )
    
    def create_performance_test(
        self,
        agent: BaseAgent,
        iterations: int = 10
    ) -> Dict[str, float]:
        """Run performance test and return metrics."""
        execution_times = []
        
        for i in range(iterations):
            start_time = time.time()
            
            try:
                result = self.run_agent_test_sync(agent)
                execution_time = time.time() - start_time
                execution_times.append(execution_time)
                
                # Validate result
                self.assert_agent_success(result)
                
            except Exception as e:
                self.fail(f"Performance test iteration {i+1} failed: {e}")
        
        return {
            "min_time": min(execution_times),
            "max_time": max(execution_times),
            "avg_time": sum(execution_times) / len(execution_times),
            "total_time": sum(execution_times),
            "iterations": iterations,
        }
    
    def create_load_test(
        self,
        agent: BaseAgent,
        concurrent_requests: int = 5,
        requests_per_client: int = 10
    ) -> Dict[str, Any]:
        """Run load test and return metrics."""
        async def client_load(client_id: int):
            """Single client load test."""
            results = []
            for i in range(requests_per_client):
                try:
                    start_time = time.time()
                    result = await self.run_agent_test(agent)
                    execution_time = time.time() - start_time
                    
                    results.append({
                        "client_id": client_id,
                        "request_id": i,
                        "execution_time": execution_time,
                        "success": True,
                        "result": result,
                    })
                except Exception as e:
                    results.append({
                        "client_id": client_id,
                        "request_id": i,
                        "execution_time": 0,
                        "success": False,
                        "error": str(e),
                    })
            return results
        
        async def run_load_test():
            """Run the complete load test."""
            tasks = [
                client_load(client_id)
                for client_id in range(concurrent_requests)
            ]
            
            all_results = await asyncio.gather(*tasks)
            return [result for client_results in all_results for result in client_results]
        
        # Run load test
        all_results = asyncio.run(run_load_test())
        
        # Calculate metrics
        successful_requests = [r for r in all_results if r["success"]]
        failed_requests = [r for r in all_results if not r["success"]]
        
        execution_times = [r["execution_time"] for r in successful_requests]
        
        return {
            "total_requests": len(all_results),
            "successful_requests": len(successful_requests),
            "failed_requests": len(failed_requests),
            "success_rate": len(successful_requests) / len(all_results) * 100,
            "avg_execution_time": sum(execution_times) / len(execution_times) if execution_times else 0,
            "min_execution_time": min(execution_times) if execution_times else 0,
            "max_execution_time": max(execution_times) if execution_times else 0,
            "concurrent_clients": concurrent_requests,
            "requests_per_client": requests_per_client,
        }


__all__ = [
    "BaseAgentTest",
    "AgentTestCase",
]
=======
Base test class for agent testing.

This module provides a comprehensive base test class that includes common
testing patterns, utilities, and assertions for agent testing.
"""

import asyncio
import logging
import time
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Type
from unittest.mock import AsyncMock, MagicMock

import pytest

from shared.core.base_agent import BaseAgent, AgentConfig
from shared.core.state import AiLexState

logger = logging.getLogger(__name__)


class BaseAgentTest:
    """
    Base test class for agent testing.
    
    This class provides common testing utilities, fixtures, and patterns
    for testing agents. It includes state management, mocking, and
    assertion utilities.
    """
    
    # Test configuration
    timeout = 30  # Default timeout for async operations
    performance_threshold = 5.0  # Default performance threshold in seconds
    
    @pytest.fixture(autouse=True)
    def setup_test(self):
        """Set up test environment for each test method."""
        self.start_time = time.time()
        self.test_data = {}
        self.mocks = {}
        
        yield
        
        # Cleanup after test
        self.cleanup_test()
    
    def cleanup_test(self):
        """Clean up test resources."""
        end_time = time.time()
        duration = end_time - self.start_time
        logger.info(f"Test completed in {duration:.2f} seconds")
        
        # Clear test data
        self.test_data.clear()
        self.mocks.clear()
    
    # ========================================================================
    # State Management Utilities
    # ========================================================================
    
    def create_test_state(
        self,
        tenant_id: Optional[str] = None,
        user_id: Optional[str] = None,
        thread_id: Optional[str] = None,
        messages: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> AiLexState:
        """
        Create a test state with default values.
        
        Args:
            tenant_id: Tenant ID (generates random if None)
            user_id: User ID (generates random if None)
            thread_id: Thread ID (generates random if None)
            messages: Initial messages
            **kwargs: Additional state fields
            
        Returns:
            AiLexState instance for testing
        """
        state = AiLexState(
            messages=messages or [],
            user_context={
                "user_id": user_id or f"user-{uuid.uuid4()}",
                "tenant_id": tenant_id or f"tenant-{uuid.uuid4()}",
                "role": "attorney",
                "assigned_case_ids": [],
                "settings": {}
            },
            thread_id=thread_id or f"thread-{uuid.uuid4()}",
            **kwargs
        )
        
        return state
    
    def add_test_message(
        self,
        state: AiLexState,
        role: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> AiLexState:
        """
        Add a test message to the state.
        
        Args:
            state: State to modify
            role: Message role
            content: Message content
            metadata: Optional metadata
            
        Returns:
            Modified state
        """
        state.add_message(role, content, metadata)
        return state
    
    # ========================================================================
    # Mock Management
    # ========================================================================
    
    def create_mock_agent(
        self,
        agent_class: Type[BaseAgent],
        config: Optional[AgentConfig] = None,
        **mock_methods
    ) -> MagicMock:
        """
        Create a mock agent with specified behavior.
        
        Args:
            agent_class: Agent class to mock
            config: Agent configuration
            **mock_methods: Methods to mock with their return values
            
        Returns:
            Mock agent instance
        """
        mock_agent = MagicMock(spec=agent_class)
        
        if config:
            mock_agent.config = config
        else:
            mock_agent.config = AgentConfig(name="MockAgent")
        
        # Set up mock methods
        for method_name, return_value in mock_methods.items():
            if asyncio.iscoroutinefunction(getattr(agent_class, method_name, None)):
                setattr(mock_agent, method_name, AsyncMock(return_value=return_value))
            else:
                setattr(mock_agent, method_name, MagicMock(return_value=return_value))
        
        self.mocks[f"agent_{agent_class.__name__}"] = mock_agent
        return mock_agent
    
    def create_mock_tool_executor(self, responses: Optional[Dict[str, Any]] = None) -> AsyncMock:
        """
        Create a mock tool executor.
        
        Args:
            responses: Tool responses mapping tool_name -> response
            
        Returns:
            Mock tool executor
        """
        mock_executor = AsyncMock()
        
        if responses:
            async def execute_tool(tool_name: str, *args, **kwargs):
                return responses.get(tool_name, {"result": "success"})
            
            mock_executor.execute_tool.side_effect = execute_tool
        else:
            mock_executor.execute_tool.return_value = {"result": "success"}
        
        self.mocks["tool_executor"] = mock_executor
        return mock_executor
    
    # ========================================================================
    # Assertion Utilities
    # ========================================================================
    
    def assert_state_valid(self, state: AiLexState):
        """
        Assert that a state is valid.
        
        Args:
            state: State to validate
        """
        assert isinstance(state, AiLexState), "State must be AiLexState instance"
        assert state.user_context is not None, "State must have user context"
        assert state.thread_id is not None, "State must have thread ID"
        assert isinstance(state.messages, list), "Messages must be a list"
    
    def assert_message_added(
        self,
        state: AiLexState,
        role: str,
        content: Optional[str] = None,
        position: int = -1
    ):
        """
        Assert that a message was added to the state.
        
        Args:
            state: State to check
            role: Expected message role
            content: Expected message content (optional)
            position: Message position (-1 for last)
        """
        assert len(state.messages) > 0, "No messages in state"
        
        message = state.messages[position]
        assert message["role"] == role, f"Expected role {role}, got {message['role']}"
        
        if content:
            assert content in message["content"], f"Content '{content}' not found in message"
    
    def assert_agent_executed(self, mock_agent: MagicMock):
        """
        Assert that an agent was executed.
        
        Args:
            mock_agent: Mock agent to check
        """
        mock_agent.initialize.assert_called_once()
        mock_agent.execute.assert_called_once()
        mock_agent.cleanup.assert_called_once()
    
    def assert_performance_within_threshold(self, duration: float, threshold: Optional[float] = None):
        """
        Assert that operation completed within performance threshold.
        
        Args:
            duration: Operation duration in seconds
            threshold: Performance threshold (uses default if None)
        """
        threshold = threshold or self.performance_threshold
        assert duration <= threshold, f"Operation took {duration:.2f}s, threshold is {threshold}s"
    
    # ========================================================================
    # Async Testing Utilities
    # ========================================================================
    
    async def run_with_timeout(self, coro, timeout: Optional[float] = None):
        """
        Run a coroutine with timeout.
        
        Args:
            coro: Coroutine to run
            timeout: Timeout in seconds (uses default if None)
            
        Returns:
            Coroutine result
        """
        timeout = timeout or self.timeout
        return await asyncio.wait_for(coro, timeout=timeout)
    
    async def measure_performance(self, coro):
        """
        Measure the performance of a coroutine.
        
        Args:
            coro: Coroutine to measure
            
        Returns:
            Tuple of (result, duration_in_seconds)
        """
        start_time = time.time()
        result = await coro
        end_time = time.time()
        duration = end_time - start_time
        
        return result, duration
>>>>>>> origin/setup/testing-infrastructure
