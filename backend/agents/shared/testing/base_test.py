"""
Base Test Classes for Agent Testing

This module provides base test classes and utilities for testing agents,
including setup/teardown, common assertions, and testing patterns.

Key Features:
- Base test class with agent-specific setup
- Common test patterns and assertions
- Mock management and cleanup
- Performance testing utilities
- Error testing patterns
- State validation helpers

Usage:
    from backend.agents.shared.testing.base_test import BaseAgentTest
    
    class TestMyAgent(BaseAgentTest):
        def setUp(self):
            super().setUp()
            self.agent = MyAgent("test")
        
        def test_agent_execution(self):
            result = self.run_agent_test(self.agent, self.sample_input)
            self.assert_agent_success(result)
"""

import unittest
from unittest.mock import MagicMock, patch, AsyncMock
from typing import Dict, Any, Optional, List, Type
import asyncio
import time
from datetime import datetime, timezone

# Local imports with fallback
try:
    from ..core.base_agent import BaseAgent
    from ..core.state import (
        BaseLangGraphState,
        AgentStatus,
        AgentExecutionContext,
        create_execution_context,
    )
    from ..core.exceptions import BaseAgentError, AgentExecutionError
    from .fixtures import (
        create_test_context,
        create_test_state,
        sample_config,
    )
    from .mocks import MockLLM, MockDatabase, MockTool
except ImportError:
    # Fallback for standalone testing
    try:
        from core.base_agent import BaseAgent
        from core.state import (
            BaseLangGraphState,
            AgentStatus,
            AgentExecutionContext,
            create_execution_context,
        )
        from core.exceptions import BaseAgentError, AgentExecutionError
        from fixtures import (
            create_test_context,
            create_test_state,
            sample_config,
        )
        from mocks import MockLLM, MockDatabase, MockTool
    except ImportError:
        # Minimal fallbacks
        class BaseAgent: pass
        class AgentStatus:
            COMPLETED = "completed"
            FAILED = "failed"
        class AgentExecutionContext: pass
        class BaseAgentError(Exception): pass
        class AgentExecutionError(BaseAgentError): pass
        def create_execution_context(**kwargs): return AgentExecutionContext()
        def create_test_context(**kwargs): return AgentExecutionContext()
        def create_test_state(context): return {}
        def sample_config(): return {}
        class MockLLM: pass
        class MockDatabase: pass
        class MockTool: pass


class BaseAgentTest(unittest.TestCase):
    """Base test class for all agent tests."""
    
    def setUp(self):
        """Set up test environment."""
        # Test configuration
        self.test_tenant_id = "test-tenant-123"
        self.test_user_id = "test-user-456"
        self.test_thread_id = "test-thread-789"
        
        # Create test context and state
        self.test_context = create_test_context(
            tenant_id=self.test_tenant_id,
            user_id=self.test_user_id,
            thread_id=self.test_thread_id
        )
        self.test_state = create_test_state(self.test_context)
        self.test_config = sample_config()
        
        # Mock objects
        self.mock_llm = MockLLM()
        self.mock_database = MockDatabase()
        self.mock_tools = {}
        
        # Performance tracking
        self.test_start_time = time.time()
        
        # Patches to clean up
        self.patches = []
    
    def tearDown(self):
        """Clean up test environment."""
        # Stop all patches
        for patch_obj in self.patches:
            patch_obj.stop()
        
        # Clear mock objects
        self.mock_llm.reset()
        self.mock_database.reset()
        
        # Log test duration
        test_duration = time.time() - self.test_start_time
        if test_duration > 5.0:  # Log slow tests
            print(f"SLOW TEST: {self._testMethodName} took {test_duration:.2f}s")
    
    def add_patch(self, target: str, **kwargs) -> MagicMock:
        """Add a patch and track it for cleanup."""
        patch_obj = patch(target, **kwargs)
        mock_obj = patch_obj.start()
        self.patches.append(patch_obj)
        return mock_obj
    
    def create_test_agent(
        self,
        agent_class: Type[BaseAgent],
        agent_type: str = "test",
        config: Optional[Dict[str, Any]] = None
    ) -> BaseAgent:
        """Create a test agent instance."""
        test_config = config or self.test_config
        return agent_class(
            agent_type=agent_type,
            config=test_config
        )
    
    async def run_agent_test(
        self,
        agent: BaseAgent,
        input_data: Optional[Dict[str, Any]] = None,
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Run an agent test with proper setup and error handling."""
        input_data = input_data or {}
        config = config or {
            "configurable": {
                "tenant_id": self.test_tenant_id,
                "user_id": self.test_user_id,
                "thread_id": self.test_thread_id,
            }
        }
        
        try:
            result = await agent.invoke(input_data, config)
            return result
        except Exception as e:
            # Capture and re-raise with context
            self.fail(f"Agent test failed: {e}")
    
    def run_agent_test_sync(
        self,
        agent: BaseAgent,
        input_data: Optional[Dict[str, Any]] = None,
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Run an agent test synchronously."""
        return asyncio.run(self.run_agent_test(agent, input_data, config))
    
    def assert_agent_success(self, result: Dict[str, Any]):
        """Assert that agent execution was successful."""
        self.assertIsInstance(result, dict)
        self.assertIn("status", result)
        self.assertEqual(result["status"], AgentStatus.COMPLETED)
        self.assertNotIn("last_error", result)
    
    def assert_agent_failure(
        self,
        result: Dict[str, Any],
        expected_error_type: Optional[str] = None
    ):
        """Assert that agent execution failed."""
        self.assertIsInstance(result, dict)
        self.assertIn("status", result)
        self.assertEqual(result["status"], AgentStatus.FAILED)
        self.assertIn("last_error", result)
        
        if expected_error_type:
            self.assertEqual(result["last_error"]["error_type"], expected_error_type)
    
    def assert_execution_context_valid(self, context: AgentExecutionContext):
        """Assert that execution context is valid."""
        self.assertIsInstance(context, AgentExecutionContext)
        self.assertIsNotNone(context.tenant_id)
        self.assertIsNotNone(context.user_id)
        self.assertIsNotNone(context.agent_type)
        self.assertIsNotNone(context.execution_id)
        self.assertIsNotNone(context.started_at)
    
    def assert_state_valid(self, state: Dict[str, Any]):
        """Assert that agent state is valid."""
        self.assertIsInstance(state, dict)
        
        # Check required fields
        required_fields = [
            "messages", "execution_context", "status",
            "memory", "context", "errors", "metadata"
        ]
        for field in required_fields:
            self.assertIn(field, state, f"Required field '{field}' missing from state")
        
        # Validate execution context
        if "execution_context" in state:
            self.assert_execution_context_valid(state["execution_context"])
    
    def assert_tenant_isolation(self, result: Dict[str, Any]):
        """Assert that tenant isolation is maintained."""
        execution_context = result.get("execution_context")
        if execution_context:
            self.assertEqual(execution_context.tenant_id, self.test_tenant_id)
    
    def assert_performance_acceptable(
        self,
        execution_time: float,
        max_time: float = 5.0
    ):
        """Assert that execution time is acceptable."""
        self.assertLessEqual(
            execution_time,
            max_time,
            f"Execution time {execution_time:.2f}s exceeds maximum {max_time}s"
        )
    
    def create_mock_tool(self, name: str, return_value: Any = None) -> MockTool:
        """Create a mock tool for testing."""
        mock_tool = MockTool(name, return_value)
        self.mock_tools[name] = mock_tool
        return mock_tool
    
    def simulate_error(
        self,
        error_type: Type[Exception] = AgentExecutionError,
        message: str = "Test error"
    ) -> Exception:
        """Create a test error for error handling tests."""
        return error_type(message)
    
    async def test_agent_lifecycle(self, agent: BaseAgent):
        """Test complete agent lifecycle."""
        # Test initialization
        state = self.test_state.copy()
        config = {"configurable": {"tenant_id": self.test_tenant_id, "user_id": self.test_user_id}}
        
        # Initialize
        state = await agent.initialize(state, config)
        self.assert_state_valid(state)
        
        # Execute
        state = await agent.execute(state, config)
        self.assert_state_valid(state)
        
        # Cleanup
        state = await agent.cleanup(state, config)
        self.assert_state_valid(state)
    
    def test_agent_configuration(self, agent: BaseAgent):
        """Test agent configuration validation."""
        self.assertIsNotNone(agent.agent_type)
        self.assertIsNotNone(agent.agent_name)
        self.assertIsInstance(agent.config, dict)
    
    def test_tenant_isolation(self, agent: BaseAgent):
        """Test tenant isolation enforcement."""
        # This should be implemented by subclasses
        pass
    
    def test_error_handling(self, agent: BaseAgent):
        """Test error handling capabilities."""
        # This should be implemented by subclasses
        pass
    
    def test_performance(self, agent: BaseAgent):
        """Test performance characteristics."""
        # This should be implemented by subclasses
        pass


class AgentTestCase(BaseAgentTest):
    """Extended test case with additional utilities."""
    
    def setUp(self):
        super().setUp()
        
        # Additional test data
        self.test_messages = [
            {"role": "user", "content": "Test message"},
            {"role": "assistant", "content": "Test response"},
        ]
        
        # Performance thresholds
        self.max_execution_time = 5.0
        self.max_memory_usage = 100 * 1024 * 1024  # 100MB
    
    def assert_memory_usage_acceptable(self, memory_usage: int):
        """Assert that memory usage is acceptable."""
        self.assertLessEqual(
            memory_usage,
            self.max_memory_usage,
            f"Memory usage {memory_usage} bytes exceeds maximum {self.max_memory_usage} bytes"
        )
    
    def create_performance_test(
        self,
        agent: BaseAgent,
        iterations: int = 10
    ) -> Dict[str, float]:
        """Run performance test and return metrics."""
        execution_times = []
        
        for i in range(iterations):
            start_time = time.time()
            
            try:
                result = self.run_agent_test_sync(agent)
                execution_time = time.time() - start_time
                execution_times.append(execution_time)
                
                # Validate result
                self.assert_agent_success(result)
                
            except Exception as e:
                self.fail(f"Performance test iteration {i+1} failed: {e}")
        
        return {
            "min_time": min(execution_times),
            "max_time": max(execution_times),
            "avg_time": sum(execution_times) / len(execution_times),
            "total_time": sum(execution_times),
            "iterations": iterations,
        }
    
    def create_load_test(
        self,
        agent: BaseAgent,
        concurrent_requests: int = 5,
        requests_per_client: int = 10
    ) -> Dict[str, Any]:
        """Run load test and return metrics."""
        async def client_load(client_id: int):
            """Single client load test."""
            results = []
            for i in range(requests_per_client):
                try:
                    start_time = time.time()
                    result = await self.run_agent_test(agent)
                    execution_time = time.time() - start_time
                    
                    results.append({
                        "client_id": client_id,
                        "request_id": i,
                        "execution_time": execution_time,
                        "success": True,
                        "result": result,
                    })
                except Exception as e:
                    results.append({
                        "client_id": client_id,
                        "request_id": i,
                        "execution_time": 0,
                        "success": False,
                        "error": str(e),
                    })
            return results
        
        async def run_load_test():
            """Run the complete load test."""
            tasks = [
                client_load(client_id)
                for client_id in range(concurrent_requests)
            ]
            
            all_results = await asyncio.gather(*tasks)
            return [result for client_results in all_results for result in client_results]
        
        # Run load test
        all_results = asyncio.run(run_load_test())
        
        # Calculate metrics
        successful_requests = [r for r in all_results if r["success"]]
        failed_requests = [r for r in all_results if not r["success"]]
        
        execution_times = [r["execution_time"] for r in successful_requests]
        
        return {
            "total_requests": len(all_results),
            "successful_requests": len(successful_requests),
            "failed_requests": len(failed_requests),
            "success_rate": len(successful_requests) / len(all_results) * 100,
            "avg_execution_time": sum(execution_times) / len(execution_times) if execution_times else 0,
            "min_execution_time": min(execution_times) if execution_times else 0,
            "max_execution_time": max(execution_times) if execution_times else 0,
            "concurrent_clients": concurrent_requests,
            "requests_per_client": requests_per_client,
        }


__all__ = [
    "BaseAgentTest",
    "AgentTestCase",
]
