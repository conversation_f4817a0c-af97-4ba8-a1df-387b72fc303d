"""
Test Fixtures for Agent Testing

This module provides test fixtures and sample data for agent testing,
including sample states, contexts, configurations, and messages.

Key Features:
- Sample execution contexts
- Sample agent states
- Sample configurations
- Sample messages and conversations
- Factory functions for test data
- Parameterized test data generation

Usage:
    from backend.agents.shared.testing.fixtures import (
        sample_execution_context,
        sample_state,
        create_test_context,
        create_test_state
    )
    
    # Use sample data
    context = sample_execution_context()
    state = sample_state(context)
    
    # Create custom test data
    custom_context = create_test_context(
        tenant_id="custom-tenant",
        agent_type="research"
    )
"""

from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
import uuid

# Local imports with fallback
try:
    from ..core.state import (
        AgentExecutionContext,
        AgentStatus,
        create_execution_context,
    )
except ImportError:
    try:
        from core.state import (
            AgentExecutionContext,
            AgentStatus,
            create_execution_context,
        )
    except ImportError:
        # Minimal fallbacks
        class AgentExecutionContext:
            def __init__(self, **kwargs):
                for k, v in kwargs.items():
                    setattr(self, k, v)

        class AgentStatus:
            PENDING = "pending"
            COMPLETED = "completed"

        def create_execution_context(**kwargs):
            return AgentExecutionContext(**kwargs)

# Try to import LangChain message types
try:
    from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
except ImportError:
    # Fallback for testing without LangChain
    class HumanMessage:
        def __init__(self, content: str):
            self.content = content
            self.type = "human"
    
    class AIMessage:
        def __init__(self, content: str):
            self.content = content
            self.type = "ai"
    
    class SystemMessage:
        def __init__(self, content: str):
            self.content = content
            self.type = "system"


def sample_execution_context(
    tenant_id: str = "test-tenant-123",
    user_id: str = "test-user-456",
    agent_type: str = "test",
    **kwargs
) -> AgentExecutionContext:
    """Create a sample execution context for testing."""
    # Extract known parameters to avoid conflicts
    thread_id = kwargs.pop("thread_id", "test-thread-789")
    user_role = kwargs.pop("user_role", "staff")
    permissions = kwargs.pop("permissions", ["read", "write"])
    max_execution_time = kwargs.pop("max_execution_time", 30)
    max_iterations = kwargs.pop("max_iterations", 10)

    return create_execution_context(
        tenant_id=tenant_id,
        user_id=user_id,
        agent_type=agent_type,
        agent_name=f"{agent_type}_agent",
        thread_id=thread_id,
        user_role=user_role,
        permissions=permissions,
        max_execution_time=max_execution_time,
        max_iterations=max_iterations,
        **kwargs
    )


def sample_state(
    execution_context: Optional[AgentExecutionContext] = None,
    status: AgentStatus = AgentStatus.PENDING,
    **kwargs
) -> Dict[str, Any]:
    """Create a sample agent state for testing."""
    if execution_context is None:
        execution_context = sample_execution_context()
    
    return {
        "messages": kwargs.get("messages", sample_messages()),
        "execution_context": execution_context,
        "status": status,
        "memory": kwargs.get("memory", {}),
        "context": kwargs.get("context", {}),
        "errors": kwargs.get("errors", []),
        "last_error": kwargs.get("last_error", None),
        "tool_calls": kwargs.get("tool_calls", []),
        "pending_actions": kwargs.get("pending_actions", []),
        "completed_actions": kwargs.get("completed_actions", []),
        "metadata": kwargs.get("metadata", {}),
        "next": kwargs.get("next", None),
    }


def sample_config(
    agent_type: str = "test",
    **kwargs
) -> Dict[str, Any]:
    """Create a sample agent configuration for testing."""
    return {
        "agent_type": agent_type,
        "max_execution_time": kwargs.get("max_execution_time", 30),
        "max_iterations": kwargs.get("max_iterations", 10),
        "streaming": kwargs.get("streaming", False),
        "debug": kwargs.get("debug", True),
        "test_mode": kwargs.get("test_mode", True),
        **kwargs
    }


def sample_messages(conversation_type: str = "simple") -> List[Any]:
    """Create sample messages for testing."""
    if conversation_type == "simple":
        return [
            HumanMessage(content="Hello, I need help with my case."),
            AIMessage(content="I'd be happy to help you with your case. What specific information do you need?"),
        ]
    
    elif conversation_type == "research":
        return [
            HumanMessage(content="What is the statute of limitations for personal injury in Texas?"),
            AIMessage(content="In Texas, the statute of limitations for personal injury claims is generally 2 years from the date of injury."),
            HumanMessage(content="Are there any exceptions to this rule?"),
        ]
    
    elif conversation_type == "intake":
        return [
            SystemMessage(content="You are a legal intake assistant. Gather client information professionally."),
            HumanMessage(content="I was injured in a car accident last month."),
            AIMessage(content="I'm sorry to hear about your accident. Let me gather some information to help you. Can you tell me the date of the accident?"),
        ]
    
    elif conversation_type == "document":
        return [
            HumanMessage(content="I need help drafting a demand letter."),
            AIMessage(content="I can help you draft a demand letter. What type of case is this for?"),
            HumanMessage(content="It's for a personal injury case from a slip and fall."),
        ]
    
    elif conversation_type == "empty":
        return []
    
    else:
        # Default to simple conversation
        return sample_messages("simple")


def sample_tool_calls() -> List[Dict[str, Any]]:
    """Create sample tool calls for testing."""
    return [
        {
            "tool_name": "search_documents",
            "tool_input": {"query": "statute of limitations Texas personal injury"},
            "tool_output": {"results": ["2 years from date of injury"]},
            "execution_time": 1.2,
            "success": True,
        },
        {
            "tool_name": "calculate_deadline",
            "tool_input": {"incident_date": "2024-01-15", "jurisdiction": "Texas"},
            "tool_output": {"deadline": "2026-01-15"},
            "execution_time": 0.5,
            "success": True,
        },
    ]


def sample_errors() -> List[Dict[str, Any]]:
    """Create sample error contexts for testing."""
    return [
        {
            "error_id": str(uuid.uuid4()),
            "category": "validation",
            "error_type": "AgentValidationError",
            "error_message": "Invalid input format",
            "occurred_at": datetime.now(timezone.utc).isoformat(),
            "is_recoverable": True,
            "recovery_suggestions": ["Check input format", "Retry with valid data"],
        }
    ]


def create_test_context(
    tenant_id: Optional[str] = None,
    user_id: Optional[str] = None,
    agent_type: str = "test",
    **kwargs
) -> AgentExecutionContext:
    """Factory function to create test execution context."""
    return sample_execution_context(
        tenant_id=tenant_id or f"test-tenant-{uuid.uuid4().hex[:8]}",
        user_id=user_id or f"test-user-{uuid.uuid4().hex[:8]}",
        agent_type=agent_type,
        **kwargs
    )


def create_test_state(
    execution_context: Optional[AgentExecutionContext] = None,
    conversation_type: str = "simple",
    include_errors: bool = False,
    include_tool_calls: bool = False,
    **kwargs
) -> Dict[str, Any]:
    """Factory function to create test agent state."""
    if execution_context is None:
        execution_context = create_test_context()
    
    state = sample_state(execution_context=execution_context, **kwargs)
    
    # Add conversation
    state["messages"] = sample_messages(conversation_type)
    
    # Add errors if requested
    if include_errors:
        state["errors"] = sample_errors()
        state["last_error"] = state["errors"][-1] if state["errors"] else None
    
    # Add tool calls if requested
    if include_tool_calls:
        state["tool_calls"] = sample_tool_calls()
        state["completed_actions"] = [
            {"action": "tool_call", "tool_name": call["tool_name"], "success": call["success"]}
            for call in state["tool_calls"]
        ]
    
    return state


def create_test_config(
    agent_type: str = "test",
    performance_mode: str = "normal",
    **kwargs
) -> Dict[str, Any]:
    """Factory function to create test configuration."""
    base_config = sample_config(agent_type=agent_type, **kwargs)
    
    if performance_mode == "fast":
        base_config.update({
            "max_execution_time": 5,
            "max_iterations": 3,
            "streaming": False,
        })
    elif performance_mode == "slow":
        base_config.update({
            "max_execution_time": 60,
            "max_iterations": 20,
            "streaming": True,
        })
    elif performance_mode == "stress":
        base_config.update({
            "max_execution_time": 1,
            "max_iterations": 1,
            "streaming": False,
        })
    
    return base_config


def create_multi_tenant_test_data(num_tenants: int = 3) -> List[Dict[str, Any]]:
    """Create test data for multiple tenants."""
    test_data = []
    
    for i in range(num_tenants):
        tenant_id = f"tenant-{i+1:03d}"
        user_id = f"user-{i+1:03d}"
        
        context = create_test_context(
            tenant_id=tenant_id,
            user_id=user_id,
            agent_type="test"
        )
        
        state = create_test_state(
            execution_context=context,
            conversation_type="simple"
        )
        
        config = create_test_config(agent_type="test")
        
        test_data.append({
            "tenant_id": tenant_id,
            "user_id": user_id,
            "context": context,
            "state": state,
            "config": config,
        })
    
    return test_data


def create_performance_test_data(
    num_requests: int = 100,
    agent_type: str = "test"
) -> List[Dict[str, Any]]:
    """Create test data for performance testing."""
    test_data = []
    
    for i in range(num_requests):
        context = create_test_context(
            tenant_id=f"perf-tenant-{i % 10}",  # 10 different tenants
            user_id=f"perf-user-{i}",
            agent_type=agent_type
        )
        
        # Vary conversation types
        conversation_types = ["simple", "research", "intake", "document"]
        conversation_type = conversation_types[i % len(conversation_types)]
        
        state = create_test_state(
            execution_context=context,
            conversation_type=conversation_type,
            include_tool_calls=(i % 3 == 0),  # Every 3rd request has tool calls
        )
        
        config = create_test_config(
            agent_type=agent_type,
            performance_mode="normal"
        )
        
        test_data.append({
            "request_id": i,
            "context": context,
            "state": state,
            "config": config,
            "conversation_type": conversation_type,
        })
    
    return test_data


__all__ = [
    # Sample data functions
    "sample_execution_context",
    "sample_state",
    "sample_config",
    "sample_messages",
    "sample_tool_calls",
    "sample_errors",
    
    # Factory functions
    "create_test_context",
    "create_test_state",
    "create_test_config",
    "create_multi_tenant_test_data",
    "create_performance_test_data",
]
