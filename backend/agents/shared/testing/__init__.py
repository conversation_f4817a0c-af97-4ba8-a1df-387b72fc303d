"""
<<<<<<< HEAD
Testing Infrastructure for Agent System

This package provides comprehensive testing infrastructure for the agent system,
including base test classes, fixtures, mocks, and testing utilities.

Key Components:
- BaseAgentTest: Base test class for all agent tests
- Test fixtures for common test data
- Mock objects for external dependencies
- Testing utilities and helpers

Usage:
    from backend.agents.shared.testing.base_test import BaseAgentTest
    from backend.agents.shared.testing.fixtures import (
        sample_execution_context,
        sample_state,
        sample_config
    )
    from backend.agents.shared.testing.mocks import MockLLM, MockDatabase
    
Example:
    class TestMyAgent(BaseAgentTest):
        def test_agent_execution(self):
            agent = MyAgent("test")
            result = self.run_agent_test(agent, self.sample_input)
            assert result["status"] == "completed"
"""

from .base_test import BaseAgentTest, AgentTestCase
from .fixtures import (
    sample_execution_context,
    sample_state,
    sample_config,
    sample_messages,
    create_test_context,
    create_test_state,
)
from .mocks import (
    MockLLM,
    MockDatabase,
    MockTool,
    MockAgent,
    create_mock_config,
)

__all__ = [
    # Base test classes
    "BaseAgentTest",
    "AgentTestCase",
    
    # Fixtures
    "sample_execution_context",
    "sample_state", 
    "sample_config",
    "sample_messages",
    "create_test_context",
    "create_test_state",
    
    # Mocks
    "MockLLM",
    "MockDatabase",
    "MockTool",
    "MockAgent",
    "create_mock_config",
=======
Testing utilities for the agent system.

This package provides comprehensive testing utilities, fixtures, and base classes
for testing agents, including mocks, assertions, performance testing tools,
and advanced testing capabilities like load testing and chaos engineering.
"""

from .base_test import BaseAgentTest
from .fixtures import AgentFixtures
from .mocks import MockManager
from .assertions import AgentAssertions
from .factories import StateFactory, AgentFactory, TestDataFactory
from .performance import PerformanceMetrics, PerformanceTester
from .test_data import TestDataManager
from .advanced_utilities import (
    LoadTester,
    LoadTestConfig,
    ChaosEngineer,
    ChaosConfig,
    WorkflowValidator,
    TestScenarioRunner
)

__all__ = [
    # Core testing utilities
    "BaseAgentTest",
    "AgentFixtures",
    "MockManager",
    "AgentAssertions",

    # Data factories and management
    "StateFactory",
    "AgentFactory",
    "TestDataFactory",
    "TestDataManager",

    # Performance testing
    "PerformanceMetrics",
    "PerformanceTester",

    # Advanced testing utilities
    "LoadTester",
    "LoadTestConfig",
    "ChaosEngineer",
    "ChaosConfig",
    "WorkflowValidator",
    "TestScenarioRunner",
>>>>>>> origin/setup/testing-infrastructure
]
