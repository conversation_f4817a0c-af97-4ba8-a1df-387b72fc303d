"""
Shared Agent Infrastructure

This package provides shared infrastructure components for all agents in the system,
establishing consistent patterns and utilities across the entire agent architecture.

Key Components:
- BaseAgent: Enhanced base class for all agents
- State Management: Comprehensive state handling with validation
- Exception Handling: Standardized error types and handling
- Utilities: Common validation, logging, monitoring, and error handling
- Testing: Base test classes and fixtures for agent testing

Usage:
    from backend.agents.shared.core.base_agent import BaseAgent
    from backend.agents.shared.core.state import BaseLangGraphState, AgentStatus
    from backend.agents.shared.core.exceptions import AgentExecutionError
    from backend.agents.shared.utils.validation import validate_tenant_access
    from backend.agents.shared.testing.base_test import BaseAgentTest

Example:
    class MyAgent(BaseAgent):
        async def execute(self, state, config):
            # Agent implementation
            return state
            
    # Testing
    class TestMyAgent(BaseAgentTest):
        def test_agent_execution(self):
            # Test implementation
            pass
"""

from backend.agents.shared.core.base_agent import BaseAgent
from backend.agents.shared.core.state import (
    BaseLangGraphState,
    AgentStatus,
    AgentExecutionContext,
)
from backend.agents.shared.core.exceptions import (
    AgentExecutionError,
    AgentConfigurationError,
    AgentValidationError,
    AgentAuthorizationError,
    AgentTimeoutError,
)

__all__ = [
    # Core classes
    "BaseAgent",
    "BaseLangGraphState", 
    "AgentStatus",
    "AgentExecutionContext",
    
    # Exception classes
    "AgentExecutionError",
    "AgentConfigurationError", 
    "AgentValidationError",
    "AgentAuthorizationError",
    "AgentTimeoutError",
]

__version__ = "1.0.0"
